<template>
  <div class="table-edit-test">
    <h2>OgwTable 编辑体验优化测试</h2>
    
    <div class="test-description">
      <h3>测试功能：</h3>
      <ul>
        <li>✅ 单击可编辑单元格立即进入编辑状态</li>
        <li>✅ 可编辑单元格有明显的视觉提示（蓝色文字、编辑图标、hover效果）</li>
        <li>✅ 编辑状态下有明显的边框和动画效果</li>
        <li>✅ 支持Tab键在可编辑单元格间导航</li>
        <li>✅ 支持Enter键保存并移动到下一行同列</li>
        <li>✅ 支持Esc键退出编辑</li>
        <li>✅ 编辑完成后有成功反馈动画</li>
        <li>✅ 编辑状态指示器显示当前编辑位置和快捷键提示</li>
      </ul>
    </div>

    <div class="table-wrapper">
      <OgwTable
        :columns="columns"
        :data="tableData"
        :show-index="true"
        @cell-change="handleCellChange"
      />
    </div>
  </div>
</template>

<script>
import OgwTable from '@/components/comTable/OgwTable.vue'

export default {
  name: 'TableEditTest',
  components: {
    OgwTable
  },
  data() {
    return {
      tableData: [
        {
          id: 1,
          name: '张三',
          age: 25,
          department: 'IT',
          salary: 8000,
          joinDate: '2023-01-15',
          status: 1,
          remark: '优秀员工'
        },
        {
          id: 2,
          name: '李四',
          age: 30,
          department: 'HR',
          salary: 7500,
          joinDate: '2022-06-20',
          status: 2,
          remark: '表现良好'
        },
        {
          id: 3,
          name: '王五',
          age: 28,
          department: 'Finance',
          salary: 9000,
          joinDate: '2023-03-10',
          status: 1,
          remark: '财务专家'
        },
        {
          id: 4,
          name: '赵六',
          age: 35,
          department: 'IT',
          salary: 12000,
          joinDate: '2021-09-05',
          status: 1,
          remark: '技术骨干'
        }
      ],
      columns: [
        {
          label: '姓名',
          prop: 'name',
          editable: true,
          validation: {
            required: true,
            type: 'text',
            minLength: 2,
            maxLength: 10,
            errorMessage: '姓名长度应在2-10个字符之间'
          }
        },
        {
          label: '年龄',
          prop: 'age',
          editable: true,
          validation: {
            required: true,
            type: 'number',
            min: 18,
            max: 65,
            errorMessage: '年龄应在18-65之间'
          }
        },
        {
          label: '部门',
          prop: 'department',
          editable: true,
          options: [
            { label: 'IT部门', value: 'IT' },
            { label: '人力资源', value: 'HR' },
            { label: '财务部', value: 'Finance' },
            { label: '市场部', value: 'Marketing' }
          ]
        },
        {
          label: '薪资',
          prop: 'salary',
          editable: true,
          validation: {
            required: true,
            type: 'decimal',
            min: 3000,
            max: 50000,
            precision: 2,
            errorMessage: '薪资应在3000-50000之间'
          }
        },
        {
          label: '入职日期',
          prop: 'joinDate',
          editable: true,
          editType: 'date',
          dateConfig: {
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            placeholder: '请选择入职日期'
          }
        },
        {
          label: '状态',
          prop: 'status',
          editable: true,
          options: [
            { label: '在职', value: 1 },
            { label: '试用期', value: 2 },
            { label: '离职', value: 3 }
          ]
        },
        {
          label: '备注',
          prop: 'remark',
          editable: true,
          validation: {
            type: 'text',
            maxLength: 100,
            errorMessage: '备注不能超过100个字符'
          }
        }
      ]
    }
  },
  methods: {
    handleCellChange({ row, prop, index, value }) {
      console.log('单元格值变化:', { row, prop, index, value })
      this.$message.success(`已更新第${index + 1}行的${prop}字段`)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-edit-test {
  padding: 20px;
  
  h2 {
    color: #303133;
    margin-bottom: 20px;
  }
  
  .test-description {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    
    h3 {
      color: #409eff;
      margin-bottom: 12px;
      font-size: 16px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
      }
    }
  }
  
  .table-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
}
</style>
